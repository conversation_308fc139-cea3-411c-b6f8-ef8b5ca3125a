print("Testing imports...")

try:
    import yaml
    print("YAML OK")
except Exception as e:
    print(f"YAML Error: {e}")

try:
    from flask import Flask
    print("Flask OK")
except Exception as e:
    print(f"Flask Error: {e}")

try:
    with open('data.yaml', 'r') as f:
        data = yaml.safe_load(f)
    print(f"Data OK - {len(data.get('details', {}))} plants")
except Exception as e:
    print(f"Data Error: {e}")

print("All tests completed!")
