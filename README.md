# 🌿 Medicinal Plant Recognition System

A comprehensive web-based system for identifying and learning about medicinal plants using computer vision and machine learning.

## 🎯 Features

- **Plant Recognition**: Upload images to identify medicinal plants
- **Comprehensive Database**: Information on 98 medicinal plants with scientific names, local names, and medicinal uses
- **Interactive Web Interface**: User-friendly Streamlit-based interface
- **Dataset Statistics**: Visualizations and analytics of the plant dataset
- **YOLO Integration**: Ready for training with YOLOv8 classification models

## 📊 Dataset Information

- **Total Classes**: 98 medicinal plants
- **Dataset Format**: YOLO classification format
- **Image Splits**: Train/Validation/Test
- **Plant Information**: Scientific names, local names, medicinal uses

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Run the Web Application

```bash
streamlit run app.py
```

The application will open in your browser at `http://localhost:8501`

### 3. Setup Training Environment (Optional)

```bash
# Check dataset and setup training
python train_model.py

# Install YOLO dependencies
python train_model.py --install-deps

# Create training scripts
python train_model.py --create-scripts
```

## 📱 Web Interface Pages

### 🏠 Home
- Overview of the system
- Featured medicinal plants
- Quick statistics

### 📸 Plant Recognition
- Upload plant images for identification
- Real-time recognition results
- Plant information display

### 📚 Plant Database
- Searchable database of all 98 plants
- Filter and sort functionality
- Detailed plant information

### 📊 Dataset Statistics
- Visual analytics of the dataset
- Plant family distributions
- Medicinal use categories

### ℹ️ About
- System information
- Technology stack details
- Usage guidelines

## 🔬 Training Your Own Model

### Prerequisites
- Python 3.8+
- CUDA-capable GPU (recommended)
- Sufficient disk space for dataset

### Training Steps

1. **Prepare Environment**:
   ```bash
   python train_model.py --install-deps --create-scripts
   ```

2. **Start Training**:
   ```bash
   python yolo_training.py
   ```

3. **Monitor Progress**:
   - Training logs will be saved in `medicinal_plants/training_*/`
   - TensorBoard logs available for visualization

4. **Use Trained Model**:
   ```bash
   python inference.py
   ```

## 📁 Project Structure

```
medicinal-plant-classifier/
├── app.py                 # Main Streamlit application
├── data.yaml             # Dataset configuration
├── requirements.txt      # Python dependencies
├── train_model.py        # Training setup script
├── README.md            # This file
├── train/               # Training images
├── valid/               # Validation images
├── test/                # Test images
└── medicinal_plants/    # Training outputs (created during training)
```

## 🌱 Plant Categories Included

The system includes 98 medicinal plants covering various categories:

- **Ayurvedic Herbs**: Ashwagandha, Brahmi, Tulsi, Neem
- **Common Medicinal Plants**: Aloe vera, Turmeric, Ginger, Amla
- **Respiratory Health**: Eucalyptus, Malabar Nut, Lemongrass
- **Digestive Health**: Mint, Coriander, Curry Leaf
- **Skin Care**: Henna, Rose, Marigold
- **And many more...

## 🔧 Configuration

### Modifying Plant Database

Edit `data.yaml` to:
- Add new plant classes
- Update plant information
- Modify dataset paths

### Customizing UI

Modify `app.py` to:
- Change interface layout
- Add new features
- Customize styling

## 📈 Model Performance

The system is designed to work with YOLOv8 classification models:

- **Input Size**: 224x224 pixels
- **Expected Accuracy**: 85-95% (depends on training)
- **Inference Speed**: Real-time on modern hardware

## ⚠️ Important Notes

- **Medical Disclaimer**: This system is for educational purposes only
- **Expert Consultation**: Always consult healthcare professionals before using plants medicinally
- **Identification Verification**: Plant identification should be verified by botanical experts
- **Traditional vs. Scientific**: Traditional uses may not be scientifically validated

## 🤝 Contributing

Contributions are welcome! You can help by:

1. **Adding Plant Images**: Contribute high-quality plant images
2. **Improving Descriptions**: Enhance plant information and medicinal uses
3. **Model Enhancement**: Improve recognition accuracy
4. **UI/UX Improvements**: Enhance user interface
5. **Documentation**: Improve documentation and tutorials

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- Plant information compiled from various botanical and medicinal sources
- YOLO framework by Ultralytics
- Streamlit for the web interface
- OpenCV and PIL for image processing

## 📞 Support

For questions, issues, or contributions:
- Create an issue in the repository
- Check the documentation
- Review existing discussions

---

**Happy Plant Recognition! 🌿**
