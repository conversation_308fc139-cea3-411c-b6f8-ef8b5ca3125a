{% extends "base.html" %}

{% block title %}Plant Database - Medicinal Plant Recognition System{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="text-center mb-4">
                <i class="fas fa-database"></i> Medicinal Plant Database
            </h1>
            <p class="text-center text-muted mb-5">
                Comprehensive information on 98 medicinal plants with scientific names and therapeutic uses
            </p>
        </div>
    </div>
    
    <!-- Search and Filter Section -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control" id="searchInput" 
                       placeholder="Search by plant name, scientific name, or medicinal use...">
                <button class="btn btn-primary" type="button" id="searchBtn">Search</button>
            </div>
        </div>
        <div class="col-lg-4">
            <select class="form-select" id="sortSelect">
                <option value="name">Sort by Name</option>
                <option value="scientific">Sort by Scientific Name</option>
                <option value="local">Sort by Local Name</option>
            </select>
        </div>
    </div>
    
    <!-- Results Info -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> 
                Showing <span id="resultCount">{{ plants|length }}</span> plants
            </div>
        </div>
    </div>
    
    <!-- Plants Grid -->
    <div class="row" id="plantsContainer">
        {% for plant in plants %}
        <div class="col-lg-6 col-xl-4 mb-4 plant-item" 
             data-name="{{ plant.name.lower() }}"
             data-scientific="{{ plant.scientific_name.lower() }}"
             data-local="{{ plant.local_name.lower() }}"
             data-uses="{{ plant.medicinal_uses.lower() }}">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-leaf"></i> {{ plant.name }}
                    </h6>
                </div>
                <div class="card-body">
                    <p class="card-text">
                        <strong>Scientific Name:</strong><br>
                        <em class="text-primary">{{ plant.scientific_name }}</em>
                    </p>
                    <p class="card-text">
                        <strong>Local Name:</strong><br>
                        {{ plant.local_name }}
                    </p>
                    <p class="card-text">
                        <strong>Medicinal Uses:</strong><br>
                        <small class="text-muted">{{ plant.medicinal_uses[:150] }}{% if plant.medicinal_uses|length > 150 %}...{% endif %}</small>
                    </p>
                </div>
                <div class="card-footer bg-transparent">
                    <button class="btn btn-outline-primary btn-sm" 
                            onclick="showPlantDetails('{{ plant.name }}', '{{ plant.scientific_name }}', '{{ plant.local_name }}', `{{ plant.medicinal_uses }}`)">
                        <i class="fas fa-info-circle"></i> View Details
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- No Results Message -->
    <div id="noResults" class="text-center mt-5" style="display: none;">
        <i class="fas fa-search fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">No plants found</h4>
        <p class="text-muted">Try adjusting your search terms</p>
    </div>
</div>

<!-- Plant Details Modal -->
<div class="modal fade" id="plantModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="modalTitle">
                    <i class="fas fa-leaf"></i> Plant Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="modalContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    const sortSelect = document.getElementById('sortSelect');
    const plantsContainer = document.getElementById('plantsContainer');
    const resultCount = document.getElementById('resultCount');
    const noResults = document.getElementById('noResults');
    
    let allPlants = Array.from(document.querySelectorAll('.plant-item'));
    
    // Search functionality
    function performSearch() {
        const query = searchInput.value.toLowerCase().trim();
        let visibleCount = 0;
        
        allPlants.forEach(plant => {
            const name = plant.dataset.name;
            const scientific = plant.dataset.scientific;
            const local = plant.dataset.local;
            const uses = plant.dataset.uses;
            
            const matches = !query || 
                           name.includes(query) || 
                           scientific.includes(query) || 
                           local.includes(query) || 
                           uses.includes(query);
            
            if (matches) {
                plant.style.display = 'block';
                visibleCount++;
            } else {
                plant.style.display = 'none';
            }
        });
        
        resultCount.textContent = visibleCount;
        
        if (visibleCount === 0) {
            noResults.style.display = 'block';
            plantsContainer.style.display = 'none';
        } else {
            noResults.style.display = 'none';
            plantsContainer.style.display = 'flex';
        }
    }
    
    // Sort functionality
    function sortPlants() {
        const sortBy = sortSelect.value;
        const visiblePlants = allPlants.filter(plant => plant.style.display !== 'none');
        
        visiblePlants.sort((a, b) => {
            let aValue, bValue;
            
            switch(sortBy) {
                case 'scientific':
                    aValue = a.dataset.scientific;
                    bValue = b.dataset.scientific;
                    break;
                case 'local':
                    aValue = a.dataset.local;
                    bValue = b.dataset.local;
                    break;
                default:
                    aValue = a.dataset.name;
                    bValue = b.dataset.name;
            }
            
            return aValue.localeCompare(bValue);
        });
        
        // Reorder DOM elements
        visiblePlants.forEach(plant => {
            plantsContainer.appendChild(plant);
        });
    }
    
    // Event listeners
    searchBtn.addEventListener('click', performSearch);
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
    
    searchInput.addEventListener('input', function() {
        if (this.value === '') {
            performSearch();
        }
    });
    
    sortSelect.addEventListener('change', sortPlants);
});

// Show plant details in modal
function showPlantDetails(name, scientific, local, uses) {
    const modalTitle = document.getElementById('modalTitle');
    const modalContent = document.getElementById('modalContent');
    
    modalTitle.innerHTML = `<i class="fas fa-leaf"></i> ${name}`;
    
    modalContent.innerHTML = `
        <div class="row">
            <div class="col-12">
                <div class="card border-0">
                    <div class="card-body">
                        <h5 class="text-primary mb-3">${name}</h5>
                        
                        <div class="mb-3">
                            <h6><i class="fas fa-microscope"></i> Scientific Name</h6>
                            <p class="text-muted"><em>${scientific}</em></p>
                        </div>
                        
                        <div class="mb-3">
                            <h6><i class="fas fa-map-marker-alt"></i> Local Name</h6>
                            <p class="text-muted">${local}</p>
                        </div>
                        
                        <div class="mb-3">
                            <h6><i class="fas fa-pills"></i> Medicinal Uses</h6>
                            <p class="text-muted">${uses}</p>
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Important:</strong> Always consult healthcare professionals before using plants medicinally. 
                            Traditional uses may not be scientifically validated.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('plantModal'));
    modal.show();
}
</script>
{% endblock %}
