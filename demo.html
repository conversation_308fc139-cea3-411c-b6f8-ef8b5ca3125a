<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medicinal Plant Recognition System - Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2E8B57;
            --secondary-color: #228B22;
            --accent-color: #90EE90;
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .navbar {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 4rem 0;
            margin-bottom: 3rem;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .metric-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            margin: 1rem 0;
        }
        
        .metric-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .plant-card {
            background: white;
            border-left: 5px solid var(--primary-color);
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 10px;
        }
        
        .btn-primary {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            border-radius: 25px;
            padding: 0.5rem 2rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#home">
                <i class="fas fa-leaf"></i> Medicinal Plant Recognition System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home"><i class="fas fa-home"></i> Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#recognition"><i class="fas fa-camera"></i> Recognition</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#database"><i class="fas fa-database"></i> Database</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#statistics"><i class="fas fa-chart-bar"></i> Statistics</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about"><i class="fas fa-info-circle"></i> About</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-4">
                        <i class="fas fa-leaf"></i> Medicinal Plant Recognition System
                    </h1>
                    <p class="lead mb-4">
                        Discover and identify medicinal plants using advanced AI technology. 
                        Learn about traditional uses, scientific names, and therapeutic properties 
                        of 98 different medicinal plants.
                    </p>
                    <div class="d-flex gap-3">
                        <a href="#recognition" class="btn btn-light btn-lg">
                            <i class="fas fa-camera"></i> Start Recognition
                        </a>
                        <a href="#database" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-database"></i> Browse Database
                        </a>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="fas fa-seedling" style="font-size: 8rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-number">98</div>
                    <h5><i class="fas fa-leaf"></i> Total Plants</h5>
                    <p class="text-muted">Medicinal plant species</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-number">98</div>
                    <h5><i class="fas fa-microscope"></i> Scientific Names</h5>
                    <p class="text-muted">Botanical classifications</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-number">200+</div>
                    <h5><i class="fas fa-pills"></i> Medicinal Uses</h5>
                    <p class="text-muted">Therapeutic applications</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-number">7500+</div>
                    <h5><i class="fas fa-images"></i> Images</h5>
                    <p class="text-muted">Training dataset</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Featured Plants Section -->
    <div class="container mt-5" id="database">
        <h2 class="text-center mb-5">
            <i class="fas fa-star"></i> Featured Medicinal Plants
        </h2>
        
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title text-primary">
                            <i class="fas fa-leaf"></i> Aloe Vera
                        </h5>
                        <p class="card-text">
                            <strong>Scientific:</strong> <em>Aloe vera</em><br>
                            <strong>Local:</strong> Aloe, Ghritkumari<br>
                            <strong>Uses:</strong> Skin healing, burns treatment, digestive health, anti-inflammatory properties
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title text-primary">
                            <i class="fas fa-leaf"></i> Neem
                        </h5>
                        <p class="card-text">
                            <strong>Scientific:</strong> <em>Azadirachta indica</em><br>
                            <strong>Local:</strong> Neem, Margosa<br>
                            <strong>Uses:</strong> Antimicrobial, skin disorders, blood purification, diabetes management
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title text-primary">
                            <i class="fas fa-leaf"></i> Tulsi
                        </h5>
                        <p class="card-text">
                            <strong>Scientific:</strong> <em>Ocimum tenuiflorum</em><br>
                            <strong>Local:</strong> Holy basil, Tulsi<br>
                            <strong>Uses:</strong> Respiratory health, immunity boost, stress relief, antimicrobial
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title text-primary">
                            <i class="fas fa-leaf"></i> Ashwagandha
                        </h5>
                        <p class="card-text">
                            <strong>Scientific:</strong> <em>Withania somnifera</em><br>
                            <strong>Local:</strong> Winter cherry, Indian ginseng<br>
                            <strong>Uses:</strong> Stress relief, adaptogen, energy enhancement, cognitive function
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title text-primary">
                            <i class="fas fa-leaf"></i> Turmeric
                        </h5>
                        <p class="card-text">
                            <strong>Scientific:</strong> <em>Curcuma longa</em><br>
                            <strong>Local:</strong> Turmeric, Haldi<br>
                            <strong>Uses:</strong> Anti-inflammatory, antimicrobial, wound healing, digestive health
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title text-primary">
                            <i class="fas fa-leaf"></i> Ginger
                        </h5>
                        <p class="card-text">
                            <strong>Scientific:</strong> <em>Zingiber officinale</em><br>
                            <strong>Local:</strong> Ginger, Adrak<br>
                            <strong>Uses:</strong> Digestive aid, nausea relief, anti-inflammatory, immune support
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recognition Demo Section -->
    <div class="container mt-5" id="recognition">
        <h2 class="text-center mb-5">
            <i class="fas fa-camera"></i> Plant Recognition Demo
        </h2>
        
        <div class="row">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-upload"></i> Upload Plant Image</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <input type="file" class="form-control" id="plantImage" accept="image/*">
                            <div class="form-text">
                                Supported formats: JPG, JPEG, PNG (Max size: 16MB)
                            </div>
                        </div>
                        
                        <div id="imagePreview" class="mb-3" style="display: none;">
                            <img id="previewImg" src="" alt="Preview" class="img-fluid rounded" style="max-height: 300px;">
                        </div>
                        
                        <button type="button" class="btn btn-primary btn-lg w-100" onclick="simulateRecognition()">
                            <i class="fas fa-search"></i> Recognize Plant (Demo)
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-leaf"></i> Recognition Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="demoResults" class="text-center text-muted">
                            <i class="fas fa-image fa-3x mb-3"></i>
                            <p>Upload an image to see demo recognition results</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Section -->
    <div class="container mt-5" id="statistics">
        <h2 class="text-center mb-5">
            <i class="fas fa-chart-bar"></i> Dataset Statistics
        </h2>
        
        <div class="row">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Plant Family Distribution</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="familyChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Medicinal Use Categories</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="usesChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- About Section -->
    <div class="container mt-5" id="about">
        <h2 class="text-center mb-5">
            <i class="fas fa-info-circle"></i> About This System
        </h2>
        
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">System Overview</h5>
                        <p class="card-text">
                            The Medicinal Plant Recognition System is an AI-powered platform designed to help identify 
                            and learn about medicinal plants using computer vision and machine learning technologies.
                        </p>
                        <p class="card-text">
                            Our system combines traditional botanical knowledge with modern artificial intelligence 
                            to provide accurate plant identification and comprehensive information about medicinal 
                            properties and traditional uses.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-cogs fa-3x mb-3"></i>
                        <h5>Technology Stack</h5>
                        <ul class="list-unstyled">
                            <li>Python & Flask</li>
                            <li>YOLOv8 AI Model</li>
                            <li>Computer Vision</li>
                            <li>Bootstrap UI</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-warning">
                    <h5 class="alert-heading">
                        <i class="fas fa-exclamation-triangle"></i> Important Disclaimer
                    </h5>
                    <hr>
                    <p class="mb-0">
                        <strong>Medical Disclaimer:</strong> This system is for educational purposes only. 
                        Always consult qualified healthcare professionals before using plants medicinally. 
                        Plant identification should be verified by botanical experts.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-leaf"></i> Medicinal Plant Recognition System</h5>
                    <p>AI-powered plant identification and information system</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>Built with ❤️ for botanical education and research</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Image preview functionality
        document.getElementById('plantImage').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('previewImg').src = e.target.result;
                    document.getElementById('imagePreview').style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });

        // Simulate plant recognition
        function simulateRecognition() {
            const plants = ['Aloe Vera', 'Neem', 'Tulsi', 'Ashwagandha', 'Turmeric', 'Ginger'];
            const randomPlant = plants[Math.floor(Math.random() * plants.length)];
            const confidence = (Math.random() * 0.2 + 0.8) * 100; // 80-100%
            
            document.getElementById('demoResults').innerHTML = `
                <div class="alert alert-success">
                    <h5 class="alert-heading">
                        <i class="fas fa-check-circle"></i> Plant Identified!
                    </h5>
                    <hr>
                    <h4 class="text-success">${randomPlant}</h4>
                    <p><strong>Confidence:</strong> ${confidence.toFixed(1)}%</p>
                    <small class="text-muted">This is a demo simulation. Actual results may vary.</small>
                </div>
            `;
        }

        // Initialize charts
        document.addEventListener('DOMContentLoaded', function() {
            // Family distribution chart
            const familyCtx = document.getElementById('familyChart').getContext('2d');
            new Chart(familyCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Lamiaceae', 'Asteraceae', 'Solanaceae', 'Fabaceae', 'Others'],
                    datasets: [{
                        data: [8, 6, 5, 7, 72],
                        backgroundColor: ['#2E8B57', '#228B22', '#32CD32', '#90EE90', '#F0F8FF'],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // Uses chart
            const usesCtx = document.getElementById('usesChart').getContext('2d');
            new Chart(usesCtx, {
                type: 'bar',
                data: {
                    labels: ['Digestive', 'Respiratory', 'Skin Care', 'Anti-inflammatory'],
                    datasets: [{
                        label: 'Number of Plants',
                        data: [25, 20, 18, 22],
                        backgroundColor: '#2E8B57'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
