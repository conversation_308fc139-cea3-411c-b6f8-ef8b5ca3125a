#!/usr/bin/env python3
"""
Test script to verify the Flask app works
"""

print("🌿 Testing Medicinal Plant Recognition System...")

try:
    import yaml
    print("✅ YAML module imported successfully")
except ImportError as e:
    print(f"❌ YAML import failed: {e}")

try:
    from flask import Flask
    print("✅ Flask imported successfully")
except ImportError as e:
    print(f"❌ Flask import failed: {e}")

try:
    with open('data.yaml', 'r', encoding='utf-8') as file:
        data = yaml.safe_load(file)
    print(f"✅ Data loaded successfully - {data.get('nc', 0)} plant classes")
except Exception as e:
    print(f"❌ Data loading failed: {e}")

print("\n🚀 Starting simple Flask server...")

app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <html>
    <head>
        <title>Medicinal Plant Recognition System</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #2E8B57; text-align: center; }
            .feature { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #2E8B57; }
            .btn { background: #2E8B57; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
            .btn:hover { background: #228B22; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🌿 Medicinal Plant Recognition System</h1>
            <p style="text-align: center; color: #666;">AI-powered plant identification and information system</p>
            
            <div class="feature">
                <h3>📸 Plant Recognition</h3>
                <p>Upload plant images for instant AI-powered identification</p>
            </div>
            
            <div class="feature">
                <h3>📚 Plant Database</h3>
                <p>Comprehensive information on 98 medicinal plants</p>
            </div>
            
            <div class="feature">
                <h3>🔬 Scientific Data</h3>
                <p>Scientific names, local names, and medicinal uses</p>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="/plants" class="btn">View Plant Database</a>
                <a href="/about" class="btn">About System</a>
            </div>
            
            <div style="text-align: center; margin-top: 30px; padding: 20px; background: #fff3cd; border-radius: 5px;">
                <strong>⚠️ Medical Disclaimer:</strong> This system is for educational purposes only. 
                Always consult healthcare professionals before using plants medicinally.
            </div>
        </div>
    </body>
    </html>
    '''

@app.route('/plants')
def plants():
    try:
        with open('data.yaml', 'r', encoding='utf-8') as file:
            data = yaml.safe_load(file)
        
        plants_html = '<h2>Plant Database</h2>'
        if 'details' in data:
            for name, info in list(data['details'].items())[:10]:  # Show first 10 plants
                plants_html += f'''
                <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;">
                    <h4 style="color: #2E8B57;">{name}</h4>
                    <p><strong>Scientific:</strong> {info.get('scientific_name', 'N/A')}</p>
                    <p><strong>Local:</strong> {info.get('local_name', 'N/A')}</p>
                    <p><strong>Uses:</strong> {info.get('medicinal_uses', 'N/A')[:200]}...</p>
                </div>
                '''
        
        return f'''
        <html>
        <head><title>Plant Database</title></head>
        <body style="font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5;">
            <div style="max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px;">
                <a href="/" style="color: #2E8B57;">← Back to Home</a>
                {plants_html}
                <p><em>Showing first 10 plants of {data.get('nc', 0)} total plants</em></p>
            </div>
        </body>
        </html>
        '''
    except Exception as e:
        return f'Error loading plants: {e}'

@app.route('/about')
def about():
    return '''
    <html>
    <head><title>About</title></head>
    <body style="font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5;">
        <div style="max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px;">
            <a href="/" style="color: #2E8B57;">← Back to Home</a>
            <h2>About This System</h2>
            <p>The Medicinal Plant Recognition System is an AI-powered platform for identifying and learning about medicinal plants.</p>
            
            <h3>Features:</h3>
            <ul>
                <li>98 medicinal plant species</li>
                <li>Scientific and local names</li>
                <li>Traditional medicinal uses</li>
                <li>YOLO-based recognition system</li>
            </ul>
            
            <h3>Technology:</h3>
            <ul>
                <li>Python Flask backend</li>
                <li>YAML data storage</li>
                <li>Computer vision ready</li>
                <li>Responsive web interface</li>
            </ul>
        </div>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🌐 Starting web server on http://localhost:5000")
    print("📱 Open your browser and visit the URL above")
    app.run(debug=True, host='0.0.0.0', port=5000)
