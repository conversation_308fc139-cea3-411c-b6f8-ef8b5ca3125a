"""
Medicinal Plant Recognition Model Training Script
This script demonstrates how to train a YOLO model for medicinal plant classification.
"""

import os
import yaml
import argparse
from pathlib import Path

def setup_training_environment():
    """Setup the training environment and check requirements"""
    print("🌿 Medicinal Plant Recognition - Training Setup")
    print("=" * 50)
    
    # Check if data.yaml exists
    if not os.path.exists('data.yaml'):
        print("❌ Error: data.yaml not found!")
        return False
    
    # Load and validate data.yaml
    try:
        with open('data.yaml', 'r') as f:
            data = yaml.safe_load(f)
        
        print(f"✅ Dataset configuration loaded")
        print(f"   - Classes: {data.get('nc', 'Unknown')}")
        print(f"   - Train path: {data.get('train', 'Unknown')}")
        print(f"   - Val path: {data.get('val', 'Unknown')}")
        print(f"   - Test path: {data.get('test', 'Unknown')}")
        
        # Check if directories exist
        for split in ['train', 'val', 'test']:
            path = data.get(split)
            if path and os.path.exists(path):
                class_count = len([d for d in os.listdir(path) if os.path.isdir(os.path.join(path, d))])
                print(f"   - {split.capitalize()} directory: ✅ ({class_count} classes)")
            else:
                print(f"   - {split.capitalize()} directory: ❌ Not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading data.yaml: {e}")
        return False

def install_yolo_requirements():
    """Install YOLOv8 requirements"""
    print("\n📦 Installing YOLOv8 requirements...")
    
    try:
        import ultralytics
        print("✅ Ultralytics already installed")
    except ImportError:
        print("📥 Installing ultralytics...")
        os.system("pip install ultralytics")
    
    try:
        import torch
        print("✅ PyTorch available")
    except ImportError:
        print("📥 Installing PyTorch...")
        os.system("pip install torch torchvision")

def create_training_script():
    """Create a comprehensive training script"""
    
    training_code = '''
"""
YOLOv8 Training Script for Medicinal Plant Recognition
"""

from ultralytics import YOLO
import yaml
import os
from datetime import datetime

def train_medicinal_plant_model():
    """Train YOLOv8 model for medicinal plant classification"""
    
    # Load a pre-trained YOLOv8 model
    model = YOLO('yolov8n-cls.pt')  # Classification model
    
    # Training parameters
    training_args = {
        'data': 'data.yaml',
        'epochs': 100,
        'imgsz': 224,
        'batch': 16,
        'lr0': 0.01,
        'patience': 20,
        'save_period': 10,
        'project': 'medicinal_plants',
        'name': f'training_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
        'exist_ok': True,
        'pretrained': True,
        'optimizer': 'SGD',
        'verbose': True,
        'seed': 42,
        'deterministic': True,
        'single_cls': False,
        'rect': False,
        'cos_lr': False,
        'close_mosaic': 10,
        'resume': False,
        'amp': True,
        'fraction': 1.0,
        'profile': False,
        'freeze': None,
        'multi_scale': False,
        'overlap_mask': True,
        'mask_ratio': 4,
        'dropout': 0.0,
        'val': True,
        'split': 'val',
        'save_json': False,
        'save_hybrid': False,
        'conf': None,
        'iou': 0.7,
        'max_det': 300,
        'half': False,
        'dnn': False,
        'plots': True,
        'source': None,
        'vid_stride': 1,
        'stream_buffer': False,
        'visualize': False,
        'augment': False,
        'agnostic_nms': False,
        'classes': None,
        'retina_masks': False,
        'embed': None,
        'show': False,
        'save_frames': False,
        'save_txt': False,
        'save_conf': False,
        'save_crop': False,
        'show_labels': True,
        'show_conf': True,
        'show_boxes': True,
        'line_width': None,
    }
    
    print("🚀 Starting training...")
    print(f"📊 Training parameters: {training_args}")
    
    # Train the model
    results = model.train(**training_args)
    
    print("✅ Training completed!")
    print(f"📈 Results: {results}")
    
    # Validate the model
    print("🔍 Validating model...")
    validation_results = model.val()
    print(f"📊 Validation results: {validation_results}")
    
    # Export the model
    print("📦 Exporting model...")
    model.export(format='onnx')
    
    return model, results

if __name__ == "__main__":
    train_medicinal_plant_model()
'''
    
    with open('yolo_training.py', 'w') as f:
        f.write(training_code)
    
    print("✅ Training script created: yolo_training.py")

def create_inference_script():
    """Create inference script for the trained model"""
    
    inference_code = '''
"""
Inference script for trained medicinal plant recognition model
"""

from ultralytics import YOLO
import cv2
import numpy as np
from PIL import Image
import yaml

class MedicinalPlantClassifier:
    def __init__(self, model_path='best.pt', data_yaml='data.yaml'):
        """Initialize the classifier with trained model"""
        self.model = YOLO(model_path)
        
        # Load class names
        with open(data_yaml, 'r') as f:
            self.data = yaml.safe_load(f)
        self.class_names = self.data.get('names', {})
        
    def predict(self, image_path, conf_threshold=0.5):
        """Predict plant class for given image"""
        results = self.model(image_path, conf=conf_threshold)
        
        predictions = []
        for result in results:
            if result.probs is not None:
                # Get top prediction
                top_class_id = result.probs.top1
                confidence = result.probs.top1conf.item()
                class_name = self.class_names.get(top_class_id, f"Class_{top_class_id}")
                
                predictions.append({
                    'class_id': top_class_id,
                    'class_name': class_name,
                    'confidence': confidence
                })
        
        return predictions
    
    def predict_batch(self, image_paths, conf_threshold=0.5):
        """Predict plant classes for multiple images"""
        results = self.model(image_paths, conf=conf_threshold)
        
        batch_predictions = []
        for result in results:
            if result.probs is not None:
                top_class_id = result.probs.top1
                confidence = result.probs.top1conf.item()
                class_name = self.class_names.get(top_class_id, f"Class_{top_class_id}")
                
                batch_predictions.append({
                    'class_id': top_class_id,
                    'class_name': class_name,
                    'confidence': confidence
                })
        
        return batch_predictions

def main():
    """Example usage of the classifier"""
    # Initialize classifier
    classifier = MedicinalPlantClassifier()
    
    # Example prediction
    image_path = "path/to/your/plant/image.jpg"
    if os.path.exists(image_path):
        predictions = classifier.predict(image_path)
        
        for pred in predictions:
            print(f"Plant: {pred['class_name']}")
            print(f"Confidence: {pred['confidence']:.2%}")

if __name__ == "__main__":
    main()
'''
    
    with open('inference.py', 'w') as f:
        f.write(inference_code)
    
    print("✅ Inference script created: inference.py")

def main():
    """Main function to setup training environment"""
    parser = argparse.ArgumentParser(description='Setup Medicinal Plant Recognition Training')
    parser.add_argument('--install-deps', action='store_true', help='Install required dependencies')
    parser.add_argument('--create-scripts', action='store_true', help='Create training and inference scripts')
    
    args = parser.parse_args()
    
    # Setup environment
    if not setup_training_environment():
        return
    
    # Install dependencies if requested
    if args.install_deps:
        install_yolo_requirements()
    
    # Create scripts if requested
    if args.create_scripts:
        create_training_script()
        create_inference_script()
    
    print("\n🎯 Next Steps:")
    print("1. Install dependencies: python train_model.py --install-deps")
    print("2. Create training scripts: python train_model.py --create-scripts")
    print("3. Run training: python yolo_training.py")
    print("4. Start web app: streamlit run app.py")
    
    print("\n📚 Training Tips:")
    print("- Ensure you have sufficient GPU memory for training")
    print("- Adjust batch size based on your hardware")
    print("- Monitor training progress and adjust hyperparameters")
    print("- Use data augmentation for better generalization")

if __name__ == "__main__":
    main()
