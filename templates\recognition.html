{% extends "base.html" %}

{% block title %}Plant Recognition - Medicinal Plant Recognition System{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="text-center mb-4">
                <i class="fas fa-camera"></i> Plant Recognition
            </h1>
            <p class="text-center text-muted mb-5">
                Upload a clear image of a medicinal plant to identify it using our AI system
            </p>
        </div>
    </div>
    
    <div class="row">
        <!-- Upload Section -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-upload"></i> Upload Plant Image</h5>
                </div>
                <div class="card-body">
                    <form id="uploadForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="plantImage" class="form-label">Choose Image File</label>
                            <input type="file" class="form-control" id="plantImage" name="file" 
                                   accept="image/*" required>
                            <div class="form-text">
                                Supported formats: JPG, JPEG, PNG (Max size: 16MB)
                            </div>
                        </div>
                        
                        <!-- Image Preview -->
                        <div id="imagePreview" class="mb-3" style="display: none;">
                            <img id="previewImg" src="" alt="Preview" class="img-fluid rounded" 
                                 style="max-height: 300px;">
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg w-100" id="recognizeBtn">
                            <i class="fas fa-search"></i> Recognize Plant
                        </button>
                    </form>
                    
                    <!-- Loading Spinner -->
                    <div id="loadingSpinner" class="text-center mt-3" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Analyzing...</span>
                        </div>
                        <p class="mt-2">Analyzing plant image...</p>
                    </div>
                </div>
            </div>
            
            <!-- Tips Card -->
            <div class="card mt-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-lightbulb"></i> Tips for Better Recognition</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li><i class="fas fa-check text-success"></i> Use clear, well-lit images</li>
                        <li><i class="fas fa-check text-success"></i> Focus on leaves or distinctive features</li>
                        <li><i class="fas fa-check text-success"></i> Avoid blurry or dark images</li>
                        <li><i class="fas fa-check text-success"></i> Include the whole leaf/flower in frame</li>
                        <li><i class="fas fa-check text-success"></i> Take photos from multiple angles</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Results Section -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-leaf"></i> Recognition Results</h5>
                </div>
                <div class="card-body">
                    <div id="noResults" class="text-center text-muted">
                        <i class="fas fa-image fa-3x mb-3"></i>
                        <p>Upload an image to see recognition results</p>
                    </div>
                    
                    <div id="results" style="display: none;">
                        <div class="alert alert-success" role="alert">
                            <h5 class="alert-heading">
                                <i class="fas fa-check-circle"></i> Plant Identified!
                            </h5>
                            <hr>
                            <div id="resultContent"></div>
                        </div>
                        
                        <div id="plantDetails" class="mt-3"></div>
                    </div>
                    
                    <div id="errorResults" style="display: none;">
                        <div class="alert alert-danger" role="alert">
                            <h5 class="alert-heading">
                                <i class="fas fa-exclamation-triangle"></i> Recognition Failed
                            </h5>
                            <hr>
                            <p id="errorMessage"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadForm = document.getElementById('uploadForm');
    const plantImage = document.getElementById('plantImage');
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    const recognizeBtn = document.getElementById('recognizeBtn');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const noResults = document.getElementById('noResults');
    const results = document.getElementById('results');
    const errorResults = document.getElementById('errorResults');
    const resultContent = document.getElementById('resultContent');
    const plantDetails = document.getElementById('plantDetails');
    const errorMessage = document.getElementById('errorMessage');
    
    // Image preview
    plantImage.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                imagePreview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            imagePreview.style.display = 'none';
        }
    });
    
    // Form submission
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData();
        const file = plantImage.files[0];
        
        if (!file) {
            alert('Please select an image file');
            return;
        }
        
        formData.append('file', file);
        
        // Show loading state
        recognizeBtn.disabled = true;
        loadingSpinner.style.display = 'block';
        noResults.style.display = 'none';
        results.style.display = 'none';
        errorResults.style.display = 'none';
        
        // Send request
        fetch('/api/recognize', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success results
                resultContent.innerHTML = `
                    <h4 class="text-success">${data.plant_name}</h4>
                    <p><strong>Confidence:</strong> ${data.confidence}</p>
                `;
                
                if (data.details) {
                    plantDetails.innerHTML = `
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-info-circle"></i> Plant Information</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>Scientific Name:</strong> <em>${data.details.scientific_name || 'N/A'}</em></p>
                                <p><strong>Local Name:</strong> ${data.details.local_name || 'N/A'}</p>
                                <p><strong>Medicinal Uses:</strong> ${data.details.medicinal_uses || 'N/A'}</p>
                            </div>
                        </div>
                    `;
                }
                
                results.style.display = 'block';
            } else {
                // Show error
                errorMessage.textContent = data.error || 'Unknown error occurred';
                errorResults.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            errorMessage.textContent = 'Network error. Please try again.';
            errorResults.style.display = 'block';
        })
        .finally(() => {
            // Hide loading state
            recognizeBtn.disabled = false;
            loadingSpinner.style.display = 'none';
        });
    });
});
</script>
{% endblock %}
