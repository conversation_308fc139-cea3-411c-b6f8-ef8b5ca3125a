"""
Simple Flask-based Medicinal Plant Recognition System
A lightweight web interface for the medicinal plant dataset
"""

from flask import Flask, render_template, request, jsonify, send_from_directory
import yaml
import os
import random
import json
from werkzeug.utils import secure_filename
from PIL import Image
import base64
import io

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Create uploads directory if it doesn't exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Load plant data
def load_plant_data():
    try:
        with open('data.yaml', 'r', encoding='utf-8') as file:
            data = yaml.safe_load(file)
        return data
    except Exception as e:
        print(f"Error loading data.yaml: {e}")
        return None

# Get sample images for demo
def get_sample_images():
    sample_images = {}
    base_dirs = ['train', 'valid', 'test']
    
    for base_dir in base_dirs:
        if os.path.exists(base_dir):
            for plant_folder in os.listdir(base_dir):
                plant_path = os.path.join(base_dir, plant_folder)
                if os.path.isdir(plant_path):
                    images = [f for f in os.listdir(plant_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                    if images and plant_folder not in sample_images:
                        sample_images[plant_folder] = os.path.join(plant_path, random.choice(images))
    return sample_images

# Simulate plant recognition
def simulate_plant_recognition(image_path):
    """Simulate plant recognition with random results for demo purposes"""
    data = load_plant_data()
    if not data or 'names' not in data:
        return None, 0.0
    
    # Randomly select a plant for demo
    plant_names = list(data['names'].values())
    predicted_plant = random.choice(plant_names)
    confidence = random.uniform(0.75, 0.98)
    
    return predicted_plant, confidence

@app.route('/')
def home():
    data = load_plant_data()
    if not data:
        return "Error: Could not load plant data", 500
    
    # Get featured plants
    featured_plants = ["Aloevera", "Neem", "Tulsi", "Ashwagandha", "Turmeric", "Ginger"]
    featured_data = []
    
    for plant in featured_plants:
        if plant in data.get('details', {}):
            plant_info = data['details'][plant]
            featured_data.append({
                'name': plant,
                'scientific_name': plant_info.get('scientific_name', 'N/A'),
                'local_name': plant_info.get('local_name', 'N/A'),
                'medicinal_uses': plant_info.get('medicinal_uses', 'N/A')[:100] + '...'
            })
    
    return render_template('home.html', 
                         total_plants=data.get('nc', 0),
                         featured_plants=featured_data)

@app.route('/recognition')
def recognition():
    return render_template('recognition.html')

@app.route('/database')
def database():
    data = load_plant_data()
    if not data:
        return "Error: Could not load plant data", 500
    
    plants = []
    if 'details' in data:
        for name, info in data['details'].items():
            plants.append({
                'name': name,
                'scientific_name': info.get('scientific_name', 'N/A'),
                'local_name': info.get('local_name', 'N/A'),
                'medicinal_uses': info.get('medicinal_uses', 'N/A')
            })
    
    return render_template('database.html', plants=plants)

@app.route('/statistics')
def statistics():
    data = load_plant_data()
    if not data:
        return "Error: Could not load plant data", 500
    
    # Generate some statistics
    stats = {
        'total_classes': data.get('nc', 0),
        'train_images': '~5000',
        'val_images': '~1500',
        'test_images': '~1000'
    }
    
    return render_template('statistics.html', stats=stats)

@app.route('/about')
def about():
    return render_template('about.html')

@app.route('/api/recognize', methods=['POST'])
def recognize_plant():
    if 'file' not in request.files:
        return jsonify({'error': 'No file uploaded'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if file:
        try:
            # Save uploaded file
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            # Simulate recognition
            predicted_plant, confidence = simulate_plant_recognition(filepath)
            
            if predicted_plant:
                # Get plant details
                data = load_plant_data()
                plant_details = {}
                if data and predicted_plant in data.get('details', {}):
                    plant_details = data['details'][predicted_plant]
                
                return jsonify({
                    'success': True,
                    'plant_name': predicted_plant,
                    'confidence': f"{confidence:.1%}",
                    'details': plant_details
                })
            else:
                return jsonify({'error': 'Could not recognize plant'}), 400
                
        except Exception as e:
            return jsonify({'error': f'Processing error: {str(e)}'}), 500
    
    return jsonify({'error': 'Invalid file'}), 400

@app.route('/api/search', methods=['GET'])
def search_plants():
    query = request.args.get('q', '').lower()
    data = load_plant_data()
    
    if not data or 'details' not in data:
        return jsonify({'error': 'No plant data available'}), 500
    
    results = []
    for name, info in data['details'].items():
        if (query in name.lower() or 
            query in info.get('scientific_name', '').lower() or
            query in info.get('local_name', '').lower() or
            query in info.get('medicinal_uses', '').lower()):
            
            results.append({
                'name': name,
                'scientific_name': info.get('scientific_name', 'N/A'),
                'local_name': info.get('local_name', 'N/A'),
                'medicinal_uses': info.get('medicinal_uses', 'N/A')
            })
    
    return jsonify({'results': results})

if __name__ == '__main__':
    print("🌿 Starting Medicinal Plant Recognition System...")
    print("📊 Loading plant database...")
    
    data = load_plant_data()
    if data:
        print(f"✅ Loaded {data.get('nc', 0)} plant classes")
        print("🚀 Starting web server...")
        print("🌐 Open your browser and go to: http://localhost:5000")
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("❌ Failed to load plant data. Please ensure data.yaml exists.")
