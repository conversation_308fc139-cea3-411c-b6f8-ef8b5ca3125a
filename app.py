import streamlit as st
import cv2
import numpy as np
import yaml
from PIL import Image
import os
import random
from pathlib import Path
import plotly.express as px
import plotly.graph_objects as go

# Set page config
st.set_page_config(
    page_title="Medicinal Plant Recognition System",
    page_icon="🌿",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #2E8B57;
        text-align: center;
        margin-bottom: 2rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    .plant-card {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 1.5rem;
        border-radius: 15px;
        margin: 1rem 0;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-left: 5px solid #2E8B57;
    }
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }
    .sidebar .sidebar-content {
        background: linear-gradient(180deg, #2E8B57 0%, #228B22 100%);
    }
    .stButton > button {
        background: linear-gradient(90deg, #2E8B57 0%, #228B22 100%);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 0.5rem 2rem;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
</style>
""", unsafe_allow_html=True)

# Load data.yaml
@st.cache_data
def load_plant_data():
    try:
        with open('data.yaml', 'r', encoding='utf-8') as file:
            data = yaml.safe_load(file)
        return data
    except Exception as e:
        st.error(f"Error loading data.yaml: {e}")
        return None

# Get random plant images for demo
@st.cache_data
def get_sample_images():
    sample_images = {}
    base_dirs = ['train', 'valid', 'test']
    
    for base_dir in base_dirs:
        if os.path.exists(base_dir):
            for plant_folder in os.listdir(base_dir):
                plant_path = os.path.join(base_dir, plant_folder)
                if os.path.isdir(plant_path):
                    images = [f for f in os.listdir(plant_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                    if images and plant_folder not in sample_images:
                        sample_images[plant_folder] = os.path.join(plant_path, random.choice(images))
    return sample_images

# Simulate plant recognition (since we don't have a trained model)
def simulate_plant_recognition(image):
    """Simulate plant recognition with random results for demo purposes"""
    data = load_plant_data()
    if not data or 'names' not in data:
        return None, 0.0
    
    # Randomly select a plant for demo
    plant_names = list(data['names'].values())
    predicted_plant = random.choice(plant_names)
    confidence = random.uniform(0.75, 0.98)
    
    return predicted_plant, confidence

# Main app
def main():
    data = load_plant_data()
    if not data:
        st.error("Failed to load plant data. Please ensure data.yaml exists.")
        return
    
    # Header
    st.markdown('<h1 class="main-header">🌿 Medicinal Plant Recognition System</h1>', unsafe_allow_html=True)
    
    # Sidebar
    st.sidebar.title("🌱 Navigation")
    page = st.sidebar.selectbox(
        "Choose a page:",
        ["🏠 Home", "📸 Plant Recognition", "📚 Plant Database", "📊 Dataset Statistics", "ℹ️ About"]
    )
    
    if page == "🏠 Home":
        show_home_page(data)
    elif page == "📸 Plant Recognition":
        show_recognition_page(data)
    elif page == "📚 Plant Database":
        show_database_page(data)
    elif page == "📊 Dataset Statistics":
        show_statistics_page(data)
    elif page == "ℹ️ About":
        show_about_page()

def show_home_page(data):
    st.markdown("### Welcome to the Medicinal Plant Recognition System!")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        <div class="metric-card">
            <h3>🌿 Total Plants</h3>
            <h2 style="color: #2E8B57;">98</h2>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div class="metric-card">
            <h3>🔬 Scientific Names</h3>
            <h2 style="color: #2E8B57;">98</h2>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown("""
        <div class="metric-card">
            <h3>💊 Medicinal Uses</h3>
            <h2 style="color: #2E8B57;">200+</h2>
        </div>
        """, unsafe_allow_html=True)
    
    st.markdown("---")
    
    # Featured plants
    st.markdown("### 🌟 Featured Medicinal Plants")
    
    featured_plants = ["Aloevera", "Neem", "Tulsi", "Ashwagandha", "Turmeric", "Ginger"]
    
    cols = st.columns(3)
    for i, plant in enumerate(featured_plants[:3]):
        with cols[i]:
            if plant in data.get('details', {}):
                plant_info = data['details'][plant]
                st.markdown(f"""
                <div class="plant-card">
                    <h4>{plant}</h4>
                    <p><strong>Scientific:</strong> {plant_info.get('scientific_name', 'N/A')}</p>
                    <p><strong>Local:</strong> {plant_info.get('local_name', 'N/A')}</p>
                    <p><strong>Uses:</strong> {plant_info.get('medicinal_uses', 'N/A')[:100]}...</p>
                </div>
                """, unsafe_allow_html=True)
    
    cols2 = st.columns(3)
    for i, plant in enumerate(featured_plants[3:]):
        with cols2[i]:
            if plant in data.get('details', {}):
                plant_info = data['details'][plant]
                st.markdown(f"""
                <div class="plant-card">
                    <h4>{plant}</h4>
                    <p><strong>Scientific:</strong> {plant_info.get('scientific_name', 'N/A')}</p>
                    <p><strong>Local:</strong> {plant_info.get('local_name', 'N/A')}</p>
                    <p><strong>Uses:</strong> {plant_info.get('medicinal_uses', 'N/A')[:100]}...</p>
                </div>
                """, unsafe_allow_html=True)

def show_recognition_page(data):
    st.markdown("### 📸 Upload Plant Image for Recognition")
    
    col1, col2 = st.columns([1, 1])
    
    with col1:
        uploaded_file = st.file_uploader(
            "Choose a plant image...", 
            type=['jpg', 'jpeg', 'png'],
            help="Upload a clear image of a medicinal plant leaf or flower"
        )
        
        if uploaded_file is not None:
            image = Image.open(uploaded_file)
            st.image(image, caption="Uploaded Image", use_column_width=True)
            
            if st.button("🔍 Recognize Plant", key="recognize_btn"):
                with st.spinner("Analyzing plant image..."):
                    predicted_plant, confidence = simulate_plant_recognition(image)
                    
                    if predicted_plant:
                        st.success(f"Plant identified with {confidence:.1%} confidence!")
                        
                        # Show results in the second column
                        with col2:
                            st.markdown("### 🎯 Recognition Results")
                            
                            # Display prediction
                            st.markdown(f"""
                            <div class="plant-card">
                                <h3 style="color: #2E8B57;">{predicted_plant}</h3>
                                <p><strong>Confidence:</strong> {confidence:.1%}</p>
                            </div>
                            """, unsafe_allow_html=True)
                            
                            # Show plant details if available
                            if predicted_plant in data.get('details', {}):
                                plant_info = data['details'][predicted_plant]
                                st.markdown("### 📋 Plant Information")
                                st.markdown(f"**Scientific Name:** {plant_info.get('scientific_name', 'N/A')}")
                                st.markdown(f"**Local Name:** {plant_info.get('local_name', 'N/A')}")
                                st.markdown(f"**Medicinal Uses:** {plant_info.get('medicinal_uses', 'N/A')}")
                    else:
                        st.error("Could not recognize the plant. Please try another image.")
    
    with col2:
        if uploaded_file is None:
            st.markdown("### 💡 Tips for Better Recognition")
            st.markdown("""
            - Use clear, well-lit images
            - Focus on leaves or distinctive features
            - Avoid blurry or dark images
            - Include the whole leaf/flower in frame
            - Take photos from multiple angles
            """)
            
            # Show sample images
            st.markdown("### 📷 Sample Images")
            sample_images = get_sample_images()
            if sample_images:
                sample_plants = list(sample_images.keys())[:3]
                for plant in sample_plants:
                    if os.path.exists(sample_images[plant]):
                        try:
                            img = Image.open(sample_images[plant])
                            st.image(img, caption=f"Sample: {plant}", width=200)
                        except:
                            pass

def show_database_page(data):
    st.markdown("### 📚 Medicinal Plant Database")
    
    # Search functionality
    search_term = st.text_input("🔍 Search plants by name, scientific name, or medicinal use:")
    
    # Filter options
    col1, col2 = st.columns(2)
    with col1:
        sort_by = st.selectbox("Sort by:", ["Name", "Scientific Name", "Local Name"])
    with col2:
        show_count = st.selectbox("Show:", [10, 25, 50, "All"])
    
    if 'details' in data:
        plants = data['details']
        
        # Filter plants based on search
        if search_term:
            filtered_plants = {}
            search_lower = search_term.lower()
            for name, info in plants.items():
                if (search_lower in name.lower() or 
                    search_lower in info.get('scientific_name', '').lower() or
                    search_lower in info.get('local_name', '').lower() or
                    search_lower in info.get('medicinal_uses', '').lower()):
                    filtered_plants[name] = info
        else:
            filtered_plants = plants
        
        # Sort plants
        if sort_by == "Scientific Name":
            sorted_plants = dict(sorted(filtered_plants.items(), 
                                      key=lambda x: x[1].get('scientific_name', '')))
        elif sort_by == "Local Name":
            sorted_plants = dict(sorted(filtered_plants.items(), 
                                      key=lambda x: x[1].get('local_name', '')))
        else:
            sorted_plants = dict(sorted(filtered_plants.items()))
        
        # Limit results
        if show_count != "All":
            sorted_plants = dict(list(sorted_plants.items())[:show_count])
        
        st.markdown(f"**Found {len(sorted_plants)} plants**")
        
        # Display plants
        for name, info in sorted_plants.items():
            with st.expander(f"🌿 {name}"):
                col1, col2 = st.columns([1, 2])
                
                with col1:
                    st.markdown(f"**Scientific Name:** {info.get('scientific_name', 'N/A')}")
                    st.markdown(f"**Local Name:** {info.get('local_name', 'N/A')}")
                
                with col2:
                    st.markdown(f"**Medicinal Uses:** {info.get('medicinal_uses', 'N/A')}")

def show_statistics_page(data):
    st.markdown("### 📊 Dataset Statistics")
    
    # Basic statistics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Classes", data.get('nc', 0))
    with col2:
        st.metric("Train Images", "~5000", help="Approximate count")
    with col3:
        st.metric("Validation Images", "~1500", help="Approximate count")
    with col4:
        st.metric("Test Images", "~1000", help="Approximate count")
    
    # Plant family distribution (simulated)
    st.markdown("### 🌱 Plant Family Distribution")
    
    families = {
        'Lamiaceae': 8, 'Asteraceae': 6, 'Solanaceae': 5, 'Fabaceae': 7,
        'Euphorbiaceae': 4, 'Rutaceae': 5, 'Malvaceae': 3, 'Amaranthaceae': 4,
        'Cucurbitaceae': 3, 'Rosaceae': 3, 'Others': 50
    }
    
    fig = px.pie(values=list(families.values()), names=list(families.keys()),
                 title="Distribution by Plant Families")
    fig.update_traces(textposition='inside', textinfo='percent+label')
    st.plotly_chart(fig, use_container_width=True)
    
    # Medicinal use categories
    st.markdown("### 💊 Common Medicinal Use Categories")
    
    use_categories = {
        'Digestive Health': 25, 'Respiratory Disorders': 20, 'Skin Conditions': 18,
        'Anti-inflammatory': 22, 'Antimicrobial': 19, 'Immunity Boost': 15,
        'Pain Relief': 12, 'Fever Reduction': 14, 'Heart Health': 10, 'Diabetes Management': 8
    }
    
    fig2 = px.bar(x=list(use_categories.keys()), y=list(use_categories.values()),
                  title="Number of Plants by Medicinal Use Category")
    fig2.update_layout(xaxis_tickangle=-45)
    st.plotly_chart(fig2, use_container_width=True)

def show_about_page():
    st.markdown("### ℹ️ About This System")
    
    st.markdown("""
    ## 🌿 Medicinal Plant Recognition System
    
    This system is designed to help identify and learn about medicinal plants using computer vision and machine learning.
    
    ### 🎯 Features:
    - **Plant Recognition**: Upload images to identify medicinal plants
    - **Comprehensive Database**: Information on 98 medicinal plants
    - **Scientific Information**: Scientific names, local names, and medicinal uses
    - **Interactive Interface**: User-friendly web interface
    
    ### 🔬 Technology Stack:
    - **Frontend**: Streamlit
    - **Computer Vision**: OpenCV, PIL
    - **Data Processing**: Pandas, NumPy
    - **Visualization**: Plotly
    - **Dataset Format**: YOLO
    
    ### 📊 Dataset Information:
    - **Total Classes**: 98 medicinal plants
    - **Image Format**: JPG/PNG
    - **Annotation Format**: YOLO format
    - **Split**: Train/Validation/Test
    
    ### ⚠️ Important Notes:
    - This is a demonstration system
    - Always consult healthcare professionals before using plants medicinally
    - Plant identification should be verified by experts
    - Traditional uses may not be scientifically validated
    
    ### 🤝 Contributing:
    This project is open for contributions. You can help by:
    - Adding more plant images
    - Improving plant descriptions
    - Enhancing the recognition model
    - Reporting issues or suggestions
    """)

if __name__ == "__main__":
    main()
