"""
Model Testing Script for Medicinal Plant Classification
Tests trained model performance and provides detailed analysis
"""

import os
import yaml
from ultralytics import YOLO
import random

def test_model(model_path='best.pt', test_dir='test', num_samples=5):
    """Test the trained model on sample images"""
    
    print("🧪 Model Testing")
    print("=" * 40)
    
    # Check if model exists
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        print("💡 Train a model first using: python train_optimized.py")
        return
    
    # Load model
    try:
        model = YOLO(model_path)
        print(f"✅ Model loaded: {model_path}")
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return
    
    # Load class names
    try:
        with open('data.yaml', 'r') as f:
            config = yaml.safe_load(f)
        class_names = list(config['names'].values())
        print(f"📊 Classes: {len(class_names)}")
    except Exception as e:
        print(f"❌ Error loading class names: {e}")
        return
    
    # Check test directory
    if not os.path.exists(test_dir):
        print(f"❌ Test directory not found: {test_dir}")
        return
    
    # Run validation on entire test set
    print(f"\n🔍 Running validation on {test_dir} set...")
    try:
        val_results = model.val(data='data.yaml')
        print(f"📈 Overall Performance:")
        print(f"   Top-1 Accuracy: {val_results.top1:.3f}")
        print(f"   Top-5 Accuracy: {val_results.top5:.3f}")
    except Exception as e:
        print(f"⚠️ Validation failed: {e}")
    
    # Test on individual samples
    print(f"\n🎯 Testing on {num_samples} random samples per class:")
    
    correct_predictions = 0
    total_predictions = 0
    class_results = {}
    
    # Get class directories
    class_dirs = [d for d in os.listdir(test_dir) 
                 if os.path.isdir(os.path.join(test_dir, d))]
    
    for class_dir in class_dirs[:10]:  # Test first 10 classes to avoid too much output
        class_path = os.path.join(test_dir, class_dir)
        
        # Get image files
        image_files = [f for f in os.listdir(class_path) 
                      if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        
        if not image_files:
            continue
        
        # Sample random images
        sample_images = random.sample(image_files, min(num_samples, len(image_files)))
        
        class_correct = 0
        class_total = 0
        
        print(f"\n📁 Testing {class_dir}:")
        
        for img_file in sample_images:
            img_path = os.path.join(class_path, img_file)
            
            try:
                # Run prediction
                results = model(img_path, verbose=False)
                
                if results[0].probs is not None:
                    # Get prediction
                    pred_class_idx = results[0].probs.top1
                    confidence = results[0].probs.top1conf.item()
                    pred_class_name = class_names[pred_class_idx]
                    
                    # Check if correct
                    is_correct = pred_class_name == class_dir
                    
                    if is_correct:
                        class_correct += 1
                        correct_predictions += 1
                    
                    class_total += 1
                    total_predictions += 1
                    
                    # Show result
                    status = "✅" if is_correct else "❌"
                    print(f"   {status} {img_file}: {pred_class_name} ({confidence:.3f})")
                    
                    if not is_correct:
                        print(f"      Expected: {class_dir}")
                
            except Exception as e:
                print(f"   ❌ Error processing {img_file}: {e}")
        
        if class_total > 0:
            class_accuracy = class_correct / class_total
            class_results[class_dir] = class_accuracy
            print(f"   📊 Class accuracy: {class_accuracy:.3f} ({class_correct}/{class_total})")
    
    # Overall results
    if total_predictions > 0:
        overall_accuracy = correct_predictions / total_predictions
        print(f"\n📈 Sample Test Results:")
        print(f"   Overall accuracy: {overall_accuracy:.3f} ({correct_predictions}/{total_predictions})")
        
        # Show best and worst performing classes
        if class_results:
            sorted_results = sorted(class_results.items(), key=lambda x: x[1], reverse=True)
            
            print(f"\n🏆 Best performing classes:")
            for class_name, accuracy in sorted_results[:3]:
                print(f"   {class_name}: {accuracy:.3f}")
            
            print(f"\n⚠️ Classes needing improvement:")
            for class_name, accuracy in sorted_results[-3:]:
                print(f"   {class_name}: {accuracy:.3f}")

def test_single_image(model_path='best.pt', image_path=None):
    """Test model on a single image"""
    
    if not image_path:
        print("❌ Please provide an image path")
        return
    
    if not os.path.exists(image_path):
        print(f"❌ Image not found: {image_path}")
        return
    
    print(f"🔍 Testing single image: {image_path}")
    
    # Load model
    try:
        model = YOLO(model_path)
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return
    
    # Load class names
    try:
        with open('data.yaml', 'r') as f:
            config = yaml.safe_load(f)
        class_names = list(config['names'].values())
    except Exception as e:
        print(f"❌ Error loading class names: {e}")
        return
    
    # Run prediction
    try:
        results = model(image_path)
        
        if results[0].probs is not None:
            # Get top 5 predictions
            top5_indices = results[0].probs.top5
            top5_confidences = results[0].probs.top5conf
            
            print(f"\n🎯 Top 5 Predictions:")
            for i, (idx, conf) in enumerate(zip(top5_indices, top5_confidences)):
                class_name = class_names[idx]
                print(f"   {i+1}. {class_name}: {conf:.3f}")
        else:
            print("❌ No predictions available")
            
    except Exception as e:
        print(f"❌ Prediction failed: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # Test single image
        image_path = sys.argv[1]
        model_path = sys.argv[2] if len(sys.argv) > 2 else 'best.pt'
        test_single_image(model_path, image_path)
    else:
        # Test on test set
        model_path = 'best.pt'
        
        # Look for trained models
        possible_paths = [
            'best.pt',
            'medicinal_plants_optimized/exp_*/weights/best.pt',
            'runs/classify/train/weights/best.pt'
        ]
        
        found_model = None
        for path_pattern in possible_paths:
            if '*' in path_pattern:
                import glob
                matches = glob.glob(path_pattern)
                if matches:
                    found_model = matches[0]
                    break
            elif os.path.exists(path_pattern):
                found_model = path_pattern
                break
        
        if found_model:
            print(f"🔍 Found model: {found_model}")
            test_model(found_model)
        else:
            print("❌ No trained model found")
            print("💡 Train a model first using: python train_optimized.py")
            print("💡 Or specify model path: python test_model.py path/to/model.pt")
