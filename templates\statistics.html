{% extends "base.html" %}

{% block title %}Statistics - Medicinal Plant Recognition System{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="text-center mb-4">
                <i class="fas fa-chart-bar"></i> Dataset Statistics
            </h1>
            <p class="text-center text-muted mb-5">
                Comprehensive analytics and insights about our medicinal plant dataset
            </p>
        </div>
    </div>
    
    <!-- Key Metrics -->
    <div class="row mb-5">
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-number">{{ stats.total_classes }}</div>
                <h5><i class="fas fa-leaf"></i> Total Classes</h5>
                <p class="text-muted">Unique plant species</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-number">{{ stats.train_images }}</div>
                <h5><i class="fas fa-images"></i> Training Images</h5>
                <p class="text-muted">For model training</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-number">{{ stats.val_images }}</div>
                <h5><i class="fas fa-check-circle"></i> Validation Images</h5>
                <p class="text-muted">For model validation</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-number">{{ stats.test_images }}</div>
                <h5><i class="fas fa-vial"></i> Test Images</h5>
                <p class="text-muted">For final testing</p>
            </div>
        </div>
    </div>
    
    <!-- Charts Section -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Plant Family Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="familyChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Medicinal Use Categories</h5>
                </div>
                <div class="card-body">
                    <canvas id="usesChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Dataset Information -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Dataset Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-folder"></i> Dataset Structure</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> YOLO Classification Format</li>
                                <li><i class="fas fa-check text-success"></i> Train/Validation/Test Split</li>
                                <li><i class="fas fa-check text-success"></i> Organized by Plant Species</li>
                                <li><i class="fas fa-check text-success"></i> High-Quality Images</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-image"></i> Image Specifications</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Format: JPG, PNG</li>
                                <li><i class="fas fa-check text-success"></i> Resolution: Variable</li>
                                <li><i class="fas fa-check text-success"></i> Color: RGB</li>
                                <li><i class="fas fa-check text-success"></i> Quality: High</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Important Notes</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-shield-alt text-warning"></i>
                            <small>For educational purposes only</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-user-md text-warning"></i>
                            <small>Consult healthcare professionals</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-search text-warning"></i>
                            <small>Verify plant identification</small>
                        </li>
                        <li>
                            <i class="fas fa-book text-warning"></i>
                            <small>Traditional uses may vary</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Performance Metrics -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0"><i class="fas fa-tachometer-alt"></i> Expected Model Performance</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <h4 class="text-primary">85-95%</h4>
                            <p class="text-muted">Expected Accuracy</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-success">< 1 sec</h4>
                            <p class="text-muted">Inference Time</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-info">224x224</h4>
                            <p class="text-muted">Input Size (px)</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-warning">YOLOv8</h4>
                            <p class="text-muted">Model Architecture</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Plant Family Distribution Chart
    const familyCtx = document.getElementById('familyChart').getContext('2d');
    new Chart(familyCtx, {
        type: 'doughnut',
        data: {
            labels: ['Lamiaceae', 'Asteraceae', 'Solanaceae', 'Fabaceae', 'Euphorbiaceae', 'Rutaceae', 'Others'],
            datasets: [{
                data: [8, 6, 5, 7, 4, 5, 63],
                backgroundColor: [
                    '#2E8B57', '#228B22', '#32CD32', '#90EE90', 
                    '#98FB98', '#00FF7F', '#F0F8FF'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Medicinal Uses Chart
    const usesCtx = document.getElementById('usesChart').getContext('2d');
    new Chart(usesCtx, {
        type: 'bar',
        data: {
            labels: ['Digestive', 'Respiratory', 'Skin Care', 'Anti-inflammatory', 'Antimicrobial', 'Immunity'],
            datasets: [{
                label: 'Number of Plants',
                data: [25, 20, 18, 22, 19, 15],
                backgroundColor: '#2E8B57',
                borderColor: '#228B22',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 5
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
});
</script>
{% endblock %}
