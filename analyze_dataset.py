"""
Dataset Analysis Script for Medicinal Plant Classification
Analyzes dataset structure and identifies potential issues
"""

import os
import yaml
from collections import Counter

def analyze_dataset():
    """Analyze the medicinal plant dataset"""
    
    print("🔍 Medicinal Plant Dataset Analysis")
    print("=" * 50)
    
    # Load data.yaml
    try:
        with open('data.yaml', 'r') as f:
            config = yaml.safe_load(f)
        print("✅ data.yaml loaded successfully")
    except Exception as e:
        print(f"❌ Error loading data.yaml: {e}")
        return
    
    # Get configuration
    train_path = config.get('train', './train')
    val_path = config.get('val', './valid')
    test_path = config.get('test', './test')
    num_classes = config.get('nc', 0)
    class_names = config.get('names', {})
    
    print(f"📊 Configuration:")
    print(f"   Classes: {num_classes}")
    print(f"   Train path: {train_path}")
    print(f"   Validation path: {val_path}")
    print(f"   Test path: {test_path}")
    
    # Analyze each split
    splits = [('Train', train_path), ('Validation', val_path), ('Test', test_path)]
    
    total_images = 0
    class_distribution = {}
    
    for split_name, split_path in splits:
        print(f"\n📁 {split_name} Set Analysis:")
        
        if not os.path.exists(split_path):
            print(f"   ❌ Directory not found: {split_path}")
            continue
        
        # Get class directories
        class_dirs = [d for d in os.listdir(split_path) 
                     if os.path.isdir(os.path.join(split_path, d))]
        
        print(f"   Classes found: {len(class_dirs)}")
        
        split_images = 0
        split_distribution = {}
        
        for class_dir in class_dirs:
            class_path = os.path.join(split_path, class_dir)
            
            # Count images
            image_files = [f for f in os.listdir(class_path) 
                          if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tiff'))]
            
            count = len(image_files)
            split_images += count
            split_distribution[class_dir] = count
            
            # Update total distribution
            if class_dir not in class_distribution:
                class_distribution[class_dir] = {'train': 0, 'val': 0, 'test': 0}
            
            split_key = 'train' if 'train' in split_path else ('val' if 'valid' in split_path else 'test')
            class_distribution[class_dir][split_key] = count
        
        total_images += split_images
        print(f"   Total images: {split_images}")
        
        if split_distribution:
            min_images = min(split_distribution.values())
            max_images = max(split_distribution.values())
            avg_images = sum(split_distribution.values()) / len(split_distribution)
            
            print(f"   Images per class: {min_images} - {max_images} (avg: {avg_images:.1f})")
            
            # Check for imbalance
            if max_images > min_images * 5:
                print(f"   ⚠️ Severe class imbalance detected!")
            elif max_images > min_images * 2:
                print(f"   ⚠️ Moderate class imbalance detected")
            
            # Show classes with few images
            low_count_classes = [cls for cls, count in split_distribution.items() if count < 10]
            if low_count_classes:
                print(f"   ⚠️ Classes with <10 images: {len(low_count_classes)}")
                for cls in low_count_classes[:5]:  # Show first 5
                    print(f"      - {cls}: {split_distribution[cls]} images")
                if len(low_count_classes) > 5:
                    print(f"      ... and {len(low_count_classes) - 5} more")
    
    print(f"\n📈 Overall Statistics:")
    print(f"   Total images: {total_images}")
    print(f"   Total classes: {len(class_distribution)}")
    
    # Overall class distribution analysis
    if class_distribution:
        total_per_class = {}
        for class_name, splits in class_distribution.items():
            total_per_class[class_name] = sum(splits.values())
        
        min_total = min(total_per_class.values())
        max_total = max(total_per_class.values())
        avg_total = sum(total_per_class.values()) / len(total_per_class)
        
        print(f"   Images per class (total): {min_total} - {max_total} (avg: {avg_total:.1f})")
        
        # Show most and least represented classes
        sorted_classes = sorted(total_per_class.items(), key=lambda x: x[1], reverse=True)
        
        print(f"\n🔝 Top 5 classes by image count:")
        for i, (class_name, count) in enumerate(sorted_classes[:5]):
            print(f"   {i+1}. {class_name}: {count} images")
        
        print(f"\n🔻 Bottom 5 classes by image count:")
        for i, (class_name, count) in enumerate(sorted_classes[-5:]):
            print(f"   {len(sorted_classes)-4+i}. {class_name}: {count} images")
    
    # Check for naming inconsistencies
    print(f"\n🔍 Data Quality Checks:")
    
    # Check if YAML classes match folder classes
    yaml_classes = set(class_names.values()) if class_names else set()
    folder_classes = set(class_distribution.keys())
    
    missing_in_yaml = folder_classes - yaml_classes
    missing_in_folders = yaml_classes - folder_classes
    
    if missing_in_yaml:
        print(f"   ⚠️ Classes in folders but not in YAML: {len(missing_in_yaml)}")
        for cls in list(missing_in_yaml)[:3]:
            print(f"      - {cls}")
        if len(missing_in_yaml) > 3:
            print(f"      ... and {len(missing_in_yaml) - 3} more")
    
    if missing_in_folders:
        print(f"   ⚠️ Classes in YAML but not in folders: {len(missing_in_folders)}")
        for cls in list(missing_in_folders)[:3]:
            print(f"      - {cls}")
        if len(missing_in_folders) > 3:
            print(f"      ... and {len(missing_in_folders) - 3} more")
    
    if not missing_in_yaml and not missing_in_folders:
        print(f"   ✅ Class names are consistent between YAML and folders")
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    
    if total_images < 1000:
        print("   📸 Consider collecting more images (target: 50+ per class)")
    
    if any(max_total > min_total * 3 for min_total, max_total in [(min(total_per_class.values()), max(total_per_class.values()))] if total_per_class):
        print("   ⚖️ Apply data augmentation to balance classes")
    
    if any(count < 20 for count in total_per_class.values() if total_per_class):
        print("   🔄 Use heavy data augmentation for classes with <20 images")
    
    print("   🎯 Training recommendations:")
    print("      - Start with yolov8s-cls.pt (small model)")
    print("      - Use batch size 8-16")
    print("      - Learning rate 0.001")
    print("      - 100-200 epochs")
    print("      - Enable data augmentation")
    
    return class_distribution

if __name__ == "__main__":
    analyze_dataset()
