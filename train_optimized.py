"""
Optimized YOLOv8 Training Script for Medicinal Plant Classification
Specifically tuned for better performance and accuracy
"""

from ultralytics import YOLO
import torch
import os
from datetime import datetime
import yaml

def train_optimized_model():
    """Train YOLOv8 model with optimized parameters for plant classification"""
    
    print("🚀 Starting Optimized Model Training...")
    
    # Check GPU availability
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🖥️ Using device: {device}")
    
    # Load dataset config
    try:
        with open('data.yaml', 'r') as f:
            data_config = yaml.safe_load(f)
    except Exception as e:
        print(f"❌ Error loading data.yaml: {e}")
        return None, None
    
    num_classes = data_config.get('nc', 98)
    print(f"📊 Training for {num_classes} classes")
    
    # Choose model size - start with smaller model for stability
    model_size = 'yolov8s-cls.pt'  # Small model for initial training
    print(f"🤖 Using model: {model_size}")
    
    # Load model
    try:
        model = YOLO(model_size)
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None, None
    
    # Optimized training parameters for medicinal plants
    training_args = {
        'data': 'data.yaml',
        'epochs': 100,      # Start with fewer epochs
        'imgsz': 224,       # Standard classification size
        'batch': 8,         # Small batch for stability
        'lr0': 0.001,       # Conservative learning rate
        'lrf': 0.01,        # Final learning rate
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3,
        'warmup_momentum': 0.8,
        'warmup_bias_lr': 0.1,
        'label_smoothing': 0.1,
        'dropout': 0.2,
        'val': True,
        'plots': True,
        'save': True,
        'save_period': 25,
        'cache': False,
        'device': device,
        'workers': 2,       # Reduced workers
        'project': 'medicinal_plants_optimized',
        'name': f'exp_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
        'exist_ok': True,
        'pretrained': True,
        'optimizer': 'AdamW',
        'verbose': True,
        'seed': 42,
        'deterministic': True,
        'cos_lr': True,
        'resume': False,
        'amp': True,
        'fraction': 1.0,
    }
    
    print("\n📋 Training Configuration:")
    for key, value in training_args.items():
        print(f"   {key}: {value}")
    
    print("\n🎯 Starting training...")
    print("⏱️ This may take several hours depending on your hardware...")
    
    try:
        # Train the model
        results = model.train(**training_args)
        
        print("\n✅ Training completed successfully!")
        print(f"📁 Results saved to: {results.save_dir}")
        
        # Find the best model
        best_model_path = os.path.join(results.save_dir, 'weights', 'best.pt')
        if os.path.exists(best_model_path):
            print(f"🏆 Best model saved at: {best_model_path}")
            
            # Load best model for validation
            best_model = YOLO(best_model_path)
            
            # Run validation
            print("\n🔍 Running final validation...")
            val_results = best_model.val()
            
            print(f"\n📊 Final Validation Results:")
            print(f"   Top-1 Accuracy: {val_results.top1:.3f}")
            print(f"   Top-5 Accuracy: {val_results.top5:.3f}")
            
            return best_model, results
        else:
            print("⚠️ Best model not found, using last model")
            return model, results
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        print("\n💡 Troubleshooting tips:")
        print("1. Reduce batch size (try batch=4 or batch=2)")
        print("2. Use CPU if GPU memory is insufficient")
        print("3. Check if all image files are valid")
        print("4. Ensure data.yaml paths are correct")
        return None, None

def quick_test_model(model_path='best.pt'):
    """Quick test of the trained model"""
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        return
    
    print(f"\n🧪 Quick Model Test: {model_path}")
    
    try:
        model = YOLO(model_path)
        
        # Test on validation set
        results = model.val()
        
        print(f"📊 Model Performance:")
        print(f"   Top-1 Accuracy: {results.top1:.3f}")
        print(f"   Top-5 Accuracy: {results.top5:.3f}")
        
        # Test on a single image if available
        test_dirs = ['test', 'valid']
        for test_dir in test_dirs:
            if os.path.exists(test_dir):
                for class_dir in os.listdir(test_dir):
                    class_path = os.path.join(test_dir, class_dir)
                    if os.path.isdir(class_path):
                        images = [f for f in os.listdir(class_path) 
                                if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                        if images:
                            test_image = os.path.join(class_path, images[0])
                            print(f"\n🔍 Testing on sample image: {test_image}")
                            
                            pred_results = model(test_image)
                            if pred_results[0].probs is not None:
                                top1_class = pred_results[0].probs.top1
                                confidence = pred_results[0].probs.top1conf.item()
                                
                                # Load class names
                                with open('data.yaml', 'r') as f:
                                    config = yaml.safe_load(f)
                                class_names = list(config['names'].values())
                                
                                predicted_class = class_names[top1_class]
                                print(f"   Predicted: {predicted_class} (confidence: {confidence:.3f})")
                                print(f"   Actual: {class_dir}")
                                print(f"   Correct: {'✅' if predicted_class == class_dir else '❌'}")
                            return
        
        print("⚠️ No test images found for quick test")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    print("🌿 Medicinal Plant Classification - Optimized Training")
    print("=" * 60)
    
    # Train the model
    model, results = train_optimized_model()
    
    if model and results:
        print("\n🎉 Training completed successfully!")
        
        # Find best model path
        best_model_path = os.path.join(results.save_dir, 'weights', 'best.pt')
        if os.path.exists(best_model_path):
            print(f"\n🧪 Running quick test...")
            quick_test_model(best_model_path)
        
        print("\n📝 Next Steps:")
        print("1. Check training plots in the output directory")
        print("2. Test the model with new images")
        print("3. If accuracy is low, try:")
        print("   - More epochs (increase to 200)")
        print("   - Data augmentation")
        print("   - Larger model (yolov8m-cls.pt)")
        print("   - Class balancing")
        
    else:
        print("\n❌ Training failed. Please check the error messages above.")
        print("\n🔧 Common solutions:")
        print("1. Install required packages: pip install ultralytics")
        print("2. Check data.yaml file format")
        print("3. Verify image file integrity")
        print("4. Try with smaller batch size")
